name: 🚀 Simple Release

on:
  push:
    tags:
      - 'v*.*.*'
  workflow_dispatch:
    inputs:
      version:
        description: 'Release version (e.g., v1.0.0)'
        required: true
        type: string

env:
  NODE_VERSION: '18'
  DOCKER_USERNAME: tongnguyen

jobs:
  # ==================== VALIDATE & BUILD RELEASE ====================

  build-release:
    name: 🏗️ Build & Release
    runs-on: ubuntu-latest

    steps:
      - name: 📦 Checkout code
        uses: actions/checkout@v4

      - name: 🏷️ Extract version
        id: version
        run: |
          if [ "${{ github.event_name }}" = "workflow_dispatch" ]; then
            VERSION="${{ github.event.inputs.version }}"
          else
            VERSION=${GITHUB_REF#refs/tags/}
          fi

          echo "version=$VERSION" >> $GITHUB_OUTPUT
          echo "Release version: $VERSION"

      - name: 📦 Setup Node.js
        uses: actions/setup-node@v4
        with:
          node-version: ${{ env.NODE_VERSION }}

      - name: 🧪 Quick test
        run: |
          # Frontend build test
          cd frontend
          npm ci --legacy-peer-deps
          npm run build

          # Backend test
          cd ../backend
          npm ci
          npm test || echo "No tests configured"

      - name: 🛠️ Set up Docker Buildx
        uses: docker/setup-buildx-action@v3

      - name: 🔐 Login to Docker Hub
        uses: docker/login-action@v3
        with:
          username: ${{ env.DOCKER_USERNAME }}
          password: ${{ secrets.DOCKER_ACCESS_TOKEN }}

      - name: 🧱 Build and push release images
        run: |
          VERSION=${{ steps.version.outputs.version }}

          # Build and push backend
          docker build -t ${{ env.DOCKER_USERNAME }}/backend:$VERSION ./backend
          docker build -t ${{ env.DOCKER_USERNAME }}/backend:latest ./backend
          docker push ${{ env.DOCKER_USERNAME }}/backend:$VERSION
          docker push ${{ env.DOCKER_USERNAME }}/backend:latest

          # Build and push frontend
          docker build -t ${{ env.DOCKER_USERNAME }}/frontend:$VERSION ./frontend
          docker build -t ${{ env.DOCKER_USERNAME }}/frontend:latest ./frontend
          docker push ${{ env.DOCKER_USERNAME }}/frontend:$VERSION
          docker push ${{ env.DOCKER_USERNAME }}/frontend:latest

  # ==================== GENERATE CHANGELOG ====================
  
  generate-changelog:
    name: 📝 Generate Changelog
    runs-on: ubuntu-latest
    needs: validate-release
    
    steps:
      - name: 📦 Checkout code
        uses: actions/checkout@v4
        with:
          fetch-depth: 0

      - name: 📝 Generate changelog
        run: |
          VERSION=${{ needs.validate-release.outputs.version }}
          
          echo "# Changelog for $VERSION" > CHANGELOG.md
          echo "" >> CHANGELOG.md
          echo "Release Date: $(date '+%Y-%m-%d')" >> CHANGELOG.md
          echo "" >> CHANGELOG.md
          
          # Get commits since last tag
          LAST_TAG=$(git describe --tags --abbrev=0 HEAD^ 2>/dev/null || echo "")
          
          if [ -n "$LAST_TAG" ]; then
            echo "## Changes since $LAST_TAG" >> CHANGELOG.md
            echo "" >> CHANGELOG.md
            
            # Categorize commits
            echo "### 🚀 Features" >> CHANGELOG.md
            git log $LAST_TAG..HEAD --oneline --grep="feat" --grep="feature" >> CHANGELOG.md || echo "No new features" >> CHANGELOG.md
            echo "" >> CHANGELOG.md
            
            echo "### 🐛 Bug Fixes" >> CHANGELOG.md
            git log $LAST_TAG..HEAD --oneline --grep="fix" --grep="bug" >> CHANGELOG.md || echo "No bug fixes" >> CHANGELOG.md
            echo "" >> CHANGELOG.md
            
            echo "### 📚 Documentation" >> CHANGELOG.md
            git log $LAST_TAG..HEAD --oneline --grep="docs" >> CHANGELOG.md || echo "No documentation changes" >> CHANGELOG.md
            echo "" >> CHANGELOG.md
            
            echo "### 🔧 Other Changes" >> CHANGELOG.md
            git log $LAST_TAG..HEAD --oneline --invert-grep --grep="feat" --grep="fix" --grep="docs" >> CHANGELOG.md || echo "No other changes" >> CHANGELOG.md
          else
            echo "## Initial Release" >> CHANGELOG.md
            echo "This is the first release of the application." >> CHANGELOG.md
          fi

      - name: 📊 Upload changelog
        uses: actions/upload-artifact@v4
        with:
          name: changelog
          path: CHANGELOG.md

  # ==================== CREATE GITHUB RELEASE ====================
  
  create-release:
    name: 🎉 Create GitHub Release
    runs-on: ubuntu-latest
    needs: [validate-release, build-release, generate-changelog]
    
    steps:
      - name: 📦 Checkout code
        uses: actions/checkout@v4

      - name: 📥 Download artifacts
        uses: actions/download-artifact@v4
        with:
          path: artifacts

      - name: 🎉 Create GitHub Release
        uses: actions/create-release@v1
        env:
          GITHUB_TOKEN: ${{ secrets.GITHUB_TOKEN }}
        with:
          tag_name: ${{ needs.validate-release.outputs.version }}
          release_name: Release ${{ needs.validate-release.outputs.version }}
          body_path: artifacts/changelog/CHANGELOG.md
          draft: false
          prerelease: ${{ needs.validate-release.outputs.is_prerelease }}

      - name: 📎 Upload release assets
        uses: actions/upload-release-asset@v1
        env:
          GITHUB_TOKEN: ${{ secrets.GITHUB_TOKEN }}
        with:
          upload_url: ${{ steps.create_release.outputs.upload_url }}
          asset_path: artifacts/release-manifest/release-manifest.md
          asset_name: docker-images.md
          asset_content_type: text/markdown

  # ==================== DEPLOYMENT ====================
  
  deploy-release:
    name: 🚀 Deploy Release
    runs-on: ubuntu-latest
    needs: [validate-release, create-release]
    environment: production
    if: needs.validate-release.outputs.is_prerelease == 'false'
    
    steps:
      - name: 🚀 Deploy to production
        run: |
          echo "🚀 Deploying release ${{ needs.validate-release.outputs.version }} to production"
          echo "Backend image: ${{ env.DOCKER_USERNAME }}/backend:${{ needs.validate-release.outputs.version }}"
          echo "Frontend image: ${{ env.DOCKER_USERNAME }}/frontend:${{ needs.validate-release.outputs.version }}"
          
          # Add your deployment commands here
          # For example:
          # kubectl set image deployment/backend backend=${{ env.DOCKER_USERNAME }}/backend:${{ needs.validate-release.outputs.version }}
          # kubectl set image deployment/frontend frontend=${{ env.DOCKER_USERNAME }}/frontend:${{ needs.validate-release.outputs.version }}

  # ==================== POST-RELEASE ====================
  
  post-release:
    name: 📢 Post-Release Tasks
    runs-on: ubuntu-latest
    needs: [validate-release, create-release]
    if: always()
    
    steps:
      - name: 📢 Notify release completion
        run: |
          echo "🎉 Release ${{ needs.validate-release.outputs.version }} completed!"
          echo "GitHub Release: https://github.com/${{ github.repository }}/releases/tag/${{ needs.validate-release.outputs.version }}"
          echo "Docker Images:"
          echo "  - ${{ env.DOCKER_USERNAME }}/backend:${{ needs.validate-release.outputs.version }}"
          echo "  - ${{ env.DOCKER_USERNAME }}/frontend:${{ needs.validate-release.outputs.version }}"
