name: 🚀 Legacy Deploy (Deprecated)

on:
  # Disabled - use ci-cd.yml instead
  workflow_dispatch:
    inputs:
      force_deploy:
        description: 'Force legacy deployment'
        required: false
        type: boolean

jobs:
  build-and-push:
    runs-on: ubuntu-latest
    env:
      DOCKER_USERNAME: tongnguyen

    steps:
      - name: 📦 Checkout code
        uses: actions/checkout@v3

      - name: 🛠️ Set up Docker Buildx
        uses: docker/setup-buildx-action@v2

      - name: 🐞 Debug secrets
        run: |
          echo "DOCKER_USERNAME is: ${{ env.DOCKER_USERNAME }}"
          echo "DOCKER_ACCESS_TOKEN starts with: $(echo ${{ secrets.DOCKER_ACCESS_TOKEN }} | cut -c 1-5)..."

      - name: 🔐 Login to Docker Hub
        uses: docker/login-action@v2
        with:
          username: ${{ env.DOCKER_USERNAME }}
          password: ${{ secrets.DOCKER_ACCESS_TOKEN }}

      - name: 🐞 Verify Docker login
        run: |
          # <PERSON><PERSON><PERSON> tra file c<PERSON><PERSON> hình <PERSON>
          if [ -f ~/.docker/config.json ]; then
            echo "Docker config file exists:"
            cat ~/.docker/config.json
          else
            echo "Docker config file not found!"
            exit 1
          fi
          # Thử pull image để kiểm tra đăng nhập
          docker pull hello-world >/dev/null 2>&1 && echo "Pull test succeeded - Logged in to Docker Hub" || (echo "Pull test failed - Not logged in to Docker Hub" && exit 1)

      - name: 🧪 Prepare Backend tags
        id: backend_tags
        run: |
          echo "TAG_LATEST=${{ env.DOCKER_USERNAME }}/backend:latest" >> $GITHUB_OUTPUT
          echo "TAG_SHA=${{ env.DOCKER_USERNAME }}/backend:${{ github.sha }}" >> $GITHUB_OUTPUT

      - name: 🧱 Build and push Backend image
        uses: docker/build-push-action@v4
        with:
          context: ./backend
          file: ./backend/Dockerfile
          push: true
          tags: |
            ${{ steps.backend_tags.outputs.TAG_LATEST }}
            ${{ steps.backend_tags.outputs.TAG_SHA }}

      - name: 🧪 Prepare Frontend tags
        id: frontend_tags
        run: |
          echo "TAG_LATEST=${{ env.DOCKER_USERNAME }}/frontend:latest" >> $GITHUB_OUTPUT
          echo "TAG_SHA=${{ env.DOCKER_USERNAME }}/frontend:${{ github.sha }}" >> $GITHUB_OUTPUT

      - name: 🧱 Build and push Frontend image
        uses: docker/build-push-action@v4
        with:
          context: ./frontend
          file: ./frontend/Dockerfile
          push: true
          tags: |
            ${{ steps.frontend_tags.outputs.TAG_LATEST }}
            ${{ steps.frontend_tags.outputs.TAG_SHA }}

      - name: 🐞 Debug tags
        run: |
          echo "Backend TAG_LATEST: ${{ steps.backend_tags.outputs.TAG_LATEST }}"
          echo "Backend TAG_SHA: ${{ steps.backend_tags.outputs.TAG_SHA }}"
          echo "Frontend TAG_LATEST: ${{ steps.frontend_tags.outputs.TAG_LATEST }}"
          echo "Frontend TAG_SHA: ${{ steps.frontend_tags.outputs.TAG_SHA }}"
