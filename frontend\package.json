{"name": "frontend", "version": "0.1.0", "private": true, "scripts": {"dev": "next dev --turbopack", "build": "next build", "start": "next start", "lint": "next lint", "lint:fix": "next lint --fix", "type-check": "tsc --noEmit", "test": "echo \"Running frontend tests...\" && npm run type-check && npm run build", "test:watch": "npm run lint -- --watch", "analyze": "ANALYZE=true npm run build"}, "dependencies": {"@types/uuid": "^10.0.0", "@uiw/react-md-editor": "^4.0.6", "axios": "^1.7.9", "clsx": "^2.1.1", "date-fns": "^4.1.0", "dotenv": "^16.4.7", "framer-motion": "^12.7.4", "js-cookie": "^3.0.5", "jwt-decode": "^4.0.0", "lucide-react": "^0.479.0", "next": "^15.3.4", "react": "^18.3.1", "react-dom": "^18.3.1", "react-hook-form": "^7.54.2", "react-hot-toast": "^2.5.2", "tailwind-merge": "^3.3.0", "uuid": "^11.1.0"}, "devDependencies": {"@eslint/eslintrc": "^3", "@types/js-cookie": "^3.0.6", "@types/node": "^20", "@types/react": "^18.3.12", "@types/react-dom": "^18.3.1", "autoprefixer": "^10.4.21", "eslint": "^9", "eslint-config-next": "15.1.7", "postcss": "^8.5.3", "tailwindcss": "^3.4.17", "typescript": "^5"}}