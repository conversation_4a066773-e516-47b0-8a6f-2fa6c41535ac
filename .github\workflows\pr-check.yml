name: 🔍 Pull Request Checks

on:
  pull_request:
    branches: [ main, develop ]
    types: [opened, synchronize, reopened]

env:
  NODE_VERSION: '18'

jobs:
  # ==================== CODE QUALITY ====================
  
  code-quality:
    name: 📝 Code Quality Check
    runs-on: ubuntu-latest
    
    steps:
      - name: 📦 Checkout code
        uses: actions/checkout@v4
        with:
          fetch-depth: 0

      - name: 📦 Setup Node.js
        uses: actions/setup-node@v4
        with:
          node-version: ${{ env.NODE_VERSION }}

      - name: 📊 Check file changes
        run: |
          echo "Changed files:"
          git diff --name-only origin/main...HEAD
          
          # Check for large files
          echo "Checking for large files..."
          find . -type f -size +10M -not -path "./node_modules/*" -not -path "./.git/*" | while read file; do
            echo "⚠️ Large file detected: $file"
          done

  # ==================== FRONTEND TESTS ====================
  
  frontend-pr-test:
    name: 🧪 Frontend PR Tests
    runs-on: ubuntu-latest
    defaults:
      run:
        working-directory: ./frontend
    
    steps:
      - name: 📦 Checkout code
        uses: actions/checkout@v4

      - name: 📦 Setup Node.js
        uses: actions/setup-node@v4
        with:
          node-version: ${{ env.NODE_VERSION }}
          cache: 'npm'
          cache-dependency-path: frontend/package-lock.json

      - name: 📥 Install dependencies
        run: npm ci

      - name: 🔍 Type checking
        run: npx tsc --noEmit

      - name: 🏗️ Build check
        run: npm run build

      - name: 📊 Bundle size analysis
        run: |
          echo "📦 Analyzing bundle size..."
          if [ -d ".next" ]; then
            du -sh .next/static/chunks/* | sort -hr | head -10
          fi

  # ==================== BACKEND TESTS ====================
  
  backend-pr-test:
    name: 🧪 Backend PR Tests
    runs-on: ubuntu-latest
    defaults:
      run:
        working-directory: ./backend
    
    services:
      mysql:
        image: mysql:8.0
        env:
          MYSQL_ROOT_PASSWORD: root
          MYSQL_DATABASE: test_db
        ports:
          - 3306:3306
        options: >-
          --health-cmd="mysqladmin ping"
          --health-interval=10s
          --health-timeout=5s
          --health-retries=3

    steps:
      - name: 📦 Checkout code
        uses: actions/checkout@v4

      - name: 📦 Setup Node.js
        uses: actions/setup-node@v4
        with:
          node-version: ${{ env.NODE_VERSION }}
          cache: 'npm'
          cache-dependency-path: backend/package-lock.json

      - name: 📥 Install dependencies
        run: npm ci

      - name: 🧪 Run tests
        run: npm test
        env:
          DB_HOST: localhost
          DB_PORT: 3306
          DB_NAME: test_db
          DB_USERNAME: root
          DB_PASSWORD: root

      - name: 🔍 API endpoint check
        run: |
          echo "🔍 Checking API endpoints..."
          # Start server in background
          npm start &
          SERVER_PID=$!
          
          # Wait for server to start
          sleep 10
          
          # Test health endpoint
          curl -f http://localhost:8080/health || echo "Health endpoint not available"
          
          # Kill server
          kill $SERVER_PID

  # ==================== SECURITY CHECK ====================
  
  security-pr-check:
    name: 🔒 Security PR Check
    runs-on: ubuntu-latest
    
    steps:
      - name: 📦 Checkout code
        uses: actions/checkout@v4

      - name: 🔍 Secret scanning
        run: |
          echo "🔍 Scanning for secrets..."
          # Check for common secret patterns
          if grep -r -E "(password|secret|key|token)\s*=\s*['\"][^'\"]{8,}" . --exclude-dir=node_modules --exclude-dir=.git; then
            echo "⚠️ Potential secrets found in code!"
            exit 1
          fi
          echo "✅ No obvious secrets found"

      - name: 🔍 Dependency vulnerability check
        run: |
          cd frontend && npm audit --audit-level=high
          cd ../backend && npm audit --audit-level=high

  # ==================== DOCKER BUILD TEST ====================
  
  docker-build-test:
    name: 🐳 Docker Build Test
    runs-on: ubuntu-latest
    
    steps:
      - name: 📦 Checkout code
        uses: actions/checkout@v4

      - name: 🛠️ Set up Docker Buildx
        uses: docker/setup-buildx-action@v3

      - name: 🧱 Test Frontend Docker build
        uses: docker/build-push-action@v5
        with:
          context: ./frontend
          file: ./frontend/Dockerfile
          push: false
          tags: test-frontend:latest

      - name: 🧱 Test Backend Docker build
        uses: docker/build-push-action@v5
        with:
          context: ./backend
          file: ./backend/Dockerfile
          push: false
          tags: test-backend:latest

  # ==================== PR SUMMARY ====================
  
  pr-summary:
    name: 📋 PR Summary
    runs-on: ubuntu-latest
    needs: [code-quality, frontend-pr-test, backend-pr-test, security-pr-check, docker-build-test]
    if: always()
    
    steps:
      - name: 📊 Generate PR summary
        uses: actions/github-script@v7
        with:
          script: |
            const results = {
              'Code Quality': '${{ needs.code-quality.result }}',
              'Frontend Tests': '${{ needs.frontend-pr-test.result }}',
              'Backend Tests': '${{ needs.backend-pr-test.result }}',
              'Security Check': '${{ needs.security-pr-check.result }}',
              'Docker Build': '${{ needs.docker-build-test.result }}'
            };
            
            let summary = '## 🔍 PR Check Results\n\n';
            let allPassed = true;
            
            for (const [check, result] of Object.entries(results)) {
              const icon = result === 'success' ? '✅' : result === 'failure' ? '❌' : '⏭️';
              summary += `${icon} **${check}**: ${result}\n`;
              if (result === 'failure') allPassed = false;
            }
            
            summary += '\n---\n';
            summary += allPassed ? 
              '🎉 **All checks passed!** This PR is ready for review.' : 
              '⚠️ **Some checks failed.** Please review and fix the issues above.';
            
            // Add summary as PR comment
            github.rest.issues.createComment({
              issue_number: context.issue.number,
              owner: context.repo.owner,
              repo: context.repo.repo,
              body: summary
            });
