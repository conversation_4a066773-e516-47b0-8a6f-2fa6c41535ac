# PowerShell script to run tests locally with Docker
Write-Host "🧪 Starting Local Docker Tests..." -ForegroundColor Green

# Function to check if Dock<PERSON> is running
function Test-DockerRunning {
    try {
        docker info | Out-Null
        return $true
    }
    catch {
        return $false
    }
}

# Function to cleanup Docker resources
function Cleanup-Docker {
    Write-Host "🧹 Cleaning up Docker resources..." -ForegroundColor Yellow
    docker-compose -f docker-compose.test.yml down -v --remove-orphans 2>$null
    docker system prune -f 2>$null
}

# Check if Docker is running
if (-not (Test-DockerRunning)) {
    Write-Host "❌ Docker is not running. Please start Docker Desktop." -ForegroundColor Red
    exit 1
}

# Cleanup any existing test containers
Cleanup-Docker

Write-Host "🐳 Starting test database..." -ForegroundColor Blue
docker-compose -f docker-compose.test.yml up -d test-db

# Wait for database to be healthy
Write-Host "⏳ Waiting for database to be ready..." -ForegroundColor Yellow
$maxAttempts = 30
$attempt = 0

do {
    $attempt++
    Start-Sleep -Seconds 2
    $dbHealth = docker-compose -f docker-compose.test.yml ps test-db --format json | ConvertFrom-Json | Select-Object -ExpandProperty Health
    
    if ($dbHealth -eq "healthy") {
        Write-Host "✅ Database is ready!" -ForegroundColor Green
        break
    }
    
    if ($attempt -ge $maxAttempts) {
        Write-Host "❌ Database failed to start within timeout" -ForegroundColor Red
        Cleanup-Docker
        exit 1
    }
    
    Write-Host "⏳ Database not ready yet... (attempt $attempt/$maxAttempts)" -ForegroundColor Yellow
} while ($true)

# Run backend tests
Write-Host "🧪 Running Backend Tests..." -ForegroundColor Blue
$backendResult = docker-compose -f docker-compose.test.yml run --rm backend-test
$backendExitCode = $LASTEXITCODE

if ($backendExitCode -eq 0) {
    Write-Host "✅ Backend tests passed!" -ForegroundColor Green
} else {
    Write-Host "❌ Backend tests failed!" -ForegroundColor Red
}

# Run frontend tests
Write-Host "🧪 Running Frontend Tests..." -ForegroundColor Blue
$frontendResult = docker-compose -f docker-compose.test.yml run --rm frontend-test
$frontendExitCode = $LASTEXITCODE

if ($frontendExitCode -eq 0) {
    Write-Host "✅ Frontend tests passed!" -ForegroundColor Green
} else {
    Write-Host "❌ Frontend tests failed!" -ForegroundColor Red
}

# Cleanup
Cleanup-Docker

# Summary
Write-Host "`n📊 Test Summary:" -ForegroundColor Cyan
Write-Host "Backend Tests: $(if ($backendExitCode -eq 0) { '✅ PASSED' } else { '❌ FAILED' })" -ForegroundColor $(if ($backendExitCode -eq 0) { 'Green' } else { 'Red' })
Write-Host "Frontend Tests: $(if ($frontendExitCode -eq 0) { '✅ PASSED' } else { '❌ FAILED' })" -ForegroundColor $(if ($frontendExitCode -eq 0) { 'Green' } else { 'Red' })

# Exit with appropriate code
if ($backendExitCode -eq 0 -and $frontendExitCode -eq 0) {
    Write-Host "`n🎉 All tests passed! Ready to push to GitHub." -ForegroundColor Green
    exit 0
} else {
    Write-Host "`n💥 Some tests failed. Please fix before pushing to GitHub." -ForegroundColor Red
    exit 1
}
